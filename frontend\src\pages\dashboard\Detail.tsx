import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import CustomGridDashboard from '@/pages/dashboard/components/CustomGridDashboard';
import Breadcrumb from '@/components/ui/Breadcrumb';
import Button from '@/components/ui/Button';
import { ConfirmModal } from '@/components/ui/ConfirmModal';
import { getDashboardById } from '@/services/dashboardService';
import { Dashboard } from '@/types/dashboard';
import { PencilIcon, CheckIcon } from '@heroicons/react/24/outline';

export default function Page() {
  const { id } = useParams<{ id: string }>();
  const [dashboard, setDashboard] = useState<Dashboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingChanges, setPendingChanges] = useState(false);

  // useEffect(() => {
  //   const fetchDashboard = async () => {
  //     if (!id) return;

  //     try {
  //       setLoading(true);
  //       const data = await getDashboardById(id);
  //       setDashboard(data);
  //     } catch (err) {
  //       const errorMessage = err instanceof Error ? err.message : 'خطا در دریافت اطلاعات داشبورد';
  //       setError(errorMessage);
  //       console.error('Error fetching dashboard:', err);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };

  //   fetchDashboard();
  // }, [id]);

  const handleEditModeToggle = () => {
    if (isEditMode && pendingChanges) {
      setShowConfirmModal(true);
    } else {
      setIsEditMode(!isEditMode);
      setPendingChanges(false);
    }
  };

  const handleConfirmSave = () => {
    // اینجا می‌توانید منطق ذخیره تغییرات را اضافه کنید
    console.log('Changes saved!');
    setIsEditMode(false);
    setPendingChanges(false);
    setShowConfirmModal(false);
  };

  const handleCancelEdit = () => {
    setIsEditMode(false);
    setPendingChanges(false);
    setShowConfirmModal(false);
  };

  const handleLayoutChange = () => {
    setPendingChanges(true);
  };

  const breadcrumbItems = [
    { label: 'خانه', href: '/dashboard' },
    { label: dashboard?.title || 'داشبورد' },
  ];

  // if (loading) {
  //   return (
  //     <div className="min-h-full p-8">
  //       <div className="flex justify-center">
  //         <div className="text-white">در حال بارگذاری...</div>
  //       </div>
  //     </div>
  //   );
  // }

  // if (error) {
  //   return (
  //     <div className="min-h-full p-8">
  //       <div className="flex justify-center">
  //         <div className="text-red-400">{error}</div>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className="min-h-full p-8">
      <div className="flex items-center justify-between">
        <Breadcrumb items={breadcrumbItems} />

        <Button
          variant={isEditMode ? 'secondary' : 'primary'}
          icon={
            isEditMode ? (
              <CheckIcon className="h-4 w-4" />
            ) : (
              <PencilIcon className="h-4 w-4" />
            )
          }
          onClick={handleEditModeToggle}
          className="flex items-center gap-2"
        >
          {isEditMode ? 'تایید تغییرات' : 'ویرایش Layout'}
        </Button>
      </div>

      <CustomGridDashboard
        isEditMode={isEditMode}
        onLayoutChange={handleLayoutChange}
      />

      <ConfirmModal
        isOpen={showConfirmModal}
        onClose={handleCancelEdit}
        onConfirm={handleConfirmSave}
        variant="primary"
        title="تایید ذخیره تغییرات"
        message="آیا می‌خواهید تغییرات اعمال شده در چیدمان نمودارها را ذخیره کنید؟"
        confirmText="ذخیره تغییرات"
        cancelText="انصراف"
        titleIcon={<CheckIcon className="h-8 w-8" />}
        confirmIcon={<CheckIcon className="h-4 w-4" />}
      />
    </div>
  );
}
