import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import CustomGridDashboard from '@/pages/dashboard/components/CustomGridDashboard';
import Breadcrumb from '@/components/ui/Breadcrumb';
import { getDashboardById } from '@/services/dashboardService';
import { Dashboard } from '@/types/dashboard';

export default function Page() {
  const { id } = useParams<{ id: string }>();
  const [dashboard, setDashboard] = useState<Dashboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // useEffect(() => {
  //   const fetchDashboard = async () => {
  //     if (!id) return;

  //     try {
  //       setLoading(true);
  //       const data = await getDashboardById(id);
  //       setDashboard(data);
  //     } catch (err) {
  //       const errorMessage = err instanceof Error ? err.message : 'خطا در دریافت اطلاعات داشبورد';
  //       setError(errorMessage);
  //       console.error('Error fetching dashboard:', err);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };

  //   fetchDashboard();
  // }, [id]);

  const breadcrumbItems = [
    { label: 'خانه', href: '/dashboard' },
    { label: dashboard?.title || 'داشبورد' },
  ];

  // if (loading) {
  //   return (
  //     <div className="min-h-full p-8">
  //       <div className="flex justify-center">
  //         <div className="text-white">در حال بارگذاری...</div>
  //       </div>
  //     </div>
  //   );
  // }

  // if (error) {
  //   return (
  //     <div className="min-h-full p-8">
  //       <div className="flex justify-center">
  //         <div className="text-red-400">{error}</div>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className="min-h-full p-8">
      <Breadcrumb items={breadcrumbItems} />
      {/* <div className="mt-4 flex flex-row flex-wrap gap-2">
        {Array.from({ length: 364 }).map((_, index) => (
          <div
            key={index}
            className="h-12 w-12 rounded-lg border border-neutral-700"
          ></div>
        ))}
      </div> */}

      <CustomGridDashboard />
    </div>
  );
}
