import React, { memo } from 'react';
import ResponsiveChartWrapper from './ResponsiveChartWrapper';
import { createChartConfig, RESPONSIVE_RULES } from './chartConfig';
import Highcharts from 'highcharts';
import HighchartsMap from 'highcharts/modules/map';
import HighchartsWordCloud from 'highcharts/modules/wordcloud';
import HighchartsNetworkGraph from 'highcharts/modules/networkgraph';
import worldMap from '@highcharts/map-collection/custom/world.topo.json';
import iranMap from '@highcharts/map-collection/countries/ir/ir-all.geo.json';

HighchartsMap(Highcharts);
HighchartsWordCloud(Highcharts);
HighchartsNetworkGraph(Highcharts);

// Types for specialized charts
export interface WordCloudData {
  name: string;
  weight: number;
}

export interface NetworkGraphData {
  nodes: Array<{
    id: string;
    name: string;
    color?: string;
  }>;
  links: Array<{
    from: string;
    to: string;
    weight?: number;
  }>;
}

export interface MapData {
  key: string;
  value: number;
  name?: string;
}

/**
 * نمایش ابری کلمات (Word Cloud)
 */
export const WordCloudChart: React.FC<{
  data?: WordCloudData[];
  title?: string;
  className?: string;
  showLegend?: boolean;
}> = memo(
  ({
    data = [
      { name: 'تلگرام', weight: 100 },
      { name: 'اینستاگرام', weight: 80 },
      { name: 'توییتر', weight: 60 },
      { name: 'یوتوب', weight: 50 },
      { name: 'لینکدین', weight: 40 },
      { name: 'فیسبوک', weight: 35 },
      { name: 'تیک‌تاک', weight: 30 },
      { name: 'اسنپ‌چت', weight: 25 },
      { name: 'پینترست', weight: 20 },
      { name: 'ردیت', weight: 15 },
    ],
    title = 'ابر کلمات',
    className = '',
    showLegend = false,
  }) => {
    const options = createChartConfig(
      {
        chart: {
          type: 'wordcloud',
        },
        title: {
          text: title,
        },
        plotOptions: {
          wordcloud: {
            allowExtendPlayingField: true,
            animation: false,
            cursor: 'pointer',
            dataLabels: {
              enabled: false,
            },
            fontFamily: 'IranYekanX',
            maxFontSize: 50,
            minFontSize: 10,
            placementStrategy: 'random',
            rotation: {
              from: -45,
              to: 45,
              orientations: 5,
            },
            spiral: 'rectangular',
            style: {
              fontWeight: 'bold',
              fontFamily: 'IranYekanX',
              textOutline: '1px black',
            },
          },
        },
        series: [
          {
            type: 'wordcloud',
            name: 'وزن',
            data: data,
          },
        ],
        responsive: RESPONSIVE_RULES,
      },
      showLegend
    );

    return (
      <ResponsiveChartWrapper
        options={options}
        className={className}
        minHeight={300}
      />
    );
  }
);

/**
 * گراف شبکه (Network Graph)
 */
export const NetworkGraph: React.FC<{
  data?: NetworkGraphData;
  title?: string;
  className?: string;
  showLegend?: boolean;
}> = ({
  data = {
    nodes: [
      { id: 'telegram', name: 'تلگرام', color: '#004748' },
      { id: 'instagram', name: 'اینستاگرام', color: '#026B6E' },
      { id: 'twitter', name: 'توییتر', color: '#048F92' },
      { id: 'youtube', name: 'یوتوب', color: '#5BF7FA' },
      { id: 'linkedin', name: 'لینکدین', color: '#9EFEFF' },
    ],
    links: [
      { from: 'telegram', to: 'instagram', weight: 5 },
      { from: 'telegram', to: 'twitter', weight: 3 },
      { from: 'instagram', to: 'youtube', weight: 4 },
      { from: 'twitter', to: 'linkedin', weight: 2 },
      { from: 'youtube', to: 'linkedin', weight: 3 },
    ],
  },
  title = 'گراف شبکه',
  className = '',
  showLegend = false,
}) => {
  const options = createChartConfig(
    {
      chart: {
        type: 'networkgraph',
      },
      title: {
        text: title,
      },
      plotOptions: {
        networkgraph: {
          keys: ['from', 'to'],
          layoutAlgorithm: {
            enableSimulation: true,
            friction: -0.9,
            linkLength: 100,
          },
          dataLabels: {
            enabled: true,
            linkFormat: '',
            style: {
              color: '#ffffff',
              fontFamily: 'IranYekanX',
              fontSize: '11px',
              textOutline: 'none',
            },
          },
          marker: {
            radius: 25,
          },
        },
      },
      series: [
        {
          type: 'networkgraph',
          name: 'شبکه',
          data: data.links,
          nodes: data.nodes,
        },
      ],
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={400}
    />
  );
};

/**
 * نقشه ایران
 */
export const IranMapChart: React.FC<{
  data?: MapData[];
  title?: string;
  className?: string;
  showLegend?: boolean;
}> = ({
  data = [
    { key: 'ir-5428', value: 85, name: 'تهران' },
    { key: 'ir-3423', value: 72, name: 'اصفهان' },
    { key: 'ir-3427', value: 68, name: 'فارس' },
    { key: 'ir-3426', value: 59, name: 'خراسان رضوی' },
    { key: 'ir-3424', value: 45, name: 'آذربایجان شرقی' },
  ],
  title = 'نقشه ایران',
  className = '',
  showLegend = false,
}) => {
  const mapData =
    data ||
    iranMap.features.map((f: any) => ({
      key: f.properties['hc-key'],
      value: Math.floor(Math.random() * 100),
    }));

  const options = createChartConfig(
    {
      chart: {
        map: iranMap as any,
        backgroundColor: 'transparent',
        zoomType: undefined,
        panning: false,
        // Remove fixed dimensions to allow dynamic resizing
        animation: false,
      },
      mapNavigation: {
        enabled: false,
        enableButtons: false,
        enableMouseWheelZoom: false,
        enableTouchZoom: false,
        enableDoubleClickZoom: false,
        buttonOptions: {
          enabled: false,
          verticalAlign: 'bottom',
        },
      },
      title: {
        text: title || undefined,
        style: {
          color: '#ffffff',
          fontFamily: 'iranyekanx',
          fontSize: '14px',
        },
      },
      legend: {
        enabled: false,
      },
      credits: {
        enabled: false,
      },
      colorAxis: {
        min: 0,
        max: 100,
        stops: [
          [0, '#CCF4F5'],
          [0.5, '#05A0A3'],
          [1, '#026B6E'],
        ],
        labels: {
          style: {
            color: '#ffffff',
            fontSize: '10px',
          },
        },
      },
      tooltip: {
        backgroundColor: '#1f2937',
        borderColor: '#374151',
        style: {
          color: '#ffffff',
          fontSize: '10px',
        },
      },
      series: [
        {
          type: 'map',
          data: mapData,
          name: 'Value',
          states: {
            hover: {
              color: '#BADA55',
            },
          },
          dataLabels: {
            enabled: false,
          },
          animation: false,
        },
      ],
      responsive: {
        rules: [
          {
            condition: {
              maxWidth: 300,
            },
            chartOptions: {
              title: {
                style: {
                  fontSize: '12px',
                },
              },
              colorAxis: {
                labels: {
                  style: {
                    fontSize: '8px',
                  },
                },
              },
            },
          },
        ],
      },
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={300}
    />
  );
};

/**
 * نقشه جهان
 */
export const WorldMapChart: React.FC<{
  data?: Array<{
    code: string;
    value: number;
    name?: string;
  }>;
  title?: string;
  className?: string;
  showLegend?: boolean;
}> = ({
  data = [
    { code: 'IR', value: 100, name: 'ایران' },
    { code: 'US', value: 85, name: 'آمریکا' },
    { code: 'DE', value: 70, name: 'آلمان' },
    { code: 'FR', value: 65, name: 'فرانسه' },
    { code: 'GB', value: 60, name: 'انگلستان' },
    { code: 'JP', value: 55, name: 'ژاپن' },
    { code: 'CN', value: 90, name: 'چین' },
  ],
  title = 'نقشه جهان',
  className = '',
  showLegend = false,
}) => {
  const options = createChartConfig(
    {
      chart: {
        type: 'map',
        map: worldMap as any,
      },
      title: {
        text: title,
      },
      mapNavigation: {
        enabled: true,
        buttonOptions: {
          verticalAlign: 'bottom',
        },
      },
      colorAxis: {
        min: 0,
        minColor: '#004748',
        maxColor: '#5BF7FA',
        labels: {
          style: {
            color: '#ffffff',
            fontFamily: 'IranYekanX',
          },
        },
      },
      plotOptions: {
        map: {
          dataLabels: {
            enabled: false,
          },
          tooltip: {
            pointFormat: '{point.name}: <b>{point.value}</b>',
          },
        },
      },
      series: [
        {
          type: 'map',
          name: 'مقدار',
          data: data.map((item) => ({
            'hc-key': item.code.toLowerCase(),
            value: item.value,
            name: item.name,
          })),
        },
      ],
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={400}
    />
  );
};
