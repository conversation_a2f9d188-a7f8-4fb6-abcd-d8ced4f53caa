import React from 'react';
import { CHART_COLORS } from './chartConfig';

// Types for display components
export interface TableData {
  headers: string[];
  rows: Array<Array<string | number>>;
}

export interface SingleValueData {
  value: string | number;
  label?: string;
  unit?: string;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: number;
}

export interface ListItem {
  id: string | number;
  title: string;
  description?: string;
  value?: string | number;
  date?: string;
  category?: string;
  url?: string;
}

/**
 * جدول داده‌ها
 */
export const DataTable: React.FC<{
  data?: TableData;
  title?: string;
  className?: string;
  maxHeight?: string;
  striped?: boolean;
  bordered?: boolean;
}> = ({
  data = {
    headers: ['نام', 'مقدار', 'درصد', 'وضعیت'],
    rows: [
      ['تلگرام', 1250, '45.2%', 'فعال'],
      ['اینستاگرام', 890, '28.7%', 'فعال'],
      ['توییتر', 456, '15.3%', 'فعال'],
      ['یوتوب', 234, '10.8%', 'غیرفعال'],
    ],
  },
  title = 'جدول داده‌ها',
  className = '',
  maxHeight = '400px',
  striped = true,
  bordered = true,
}) => {
  return (
    <div className={`h-full w-full rounded-[5px] border border-[#323538] bg-[#181A1B] p-4 ${className}`}>
      {title && (
        <h3 className="mb-4 text-lg font-semibold text-white" style={{ fontFamily: 'IranYekanX' }}>
          {title}
        </h3>
      )}
      <div 
        className="overflow-auto"
        style={{ maxHeight }}
      >
        <table className="w-full text-sm">
          <thead>
            <tr className={`${bordered ? 'border-b border-[#323538]' : ''}`}>
              {data.headers.map((header, index) => (
                <th
                  key={index}
                  className="px-4 py-3 text-right font-semibold text-white"
                  style={{ 
                    fontFamily: 'IranYekanX',
                    backgroundColor: CHART_COLORS[0],
                  }}
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.rows.map((row, rowIndex) => (
              <tr
                key={rowIndex}
                className={`
                  ${striped && rowIndex % 2 === 1 ? 'bg-[#1F2937]' : ''}
                  ${bordered ? 'border-b border-[#323538]' : ''}
                  hover:bg-[#374151] transition-colors
                `}
              >
                {row.map((cell, cellIndex) => (
                  <td
                    key={cellIndex}
                    className="px-4 py-3 text-right text-gray-300"
                    style={{ fontFamily: 'IranYekanX' }}
                  >
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

/**
 * نمایش تک مقداری
 */
export const SingleValueDisplay: React.FC<{
  data?: SingleValueData;
  title?: string;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  showTrend?: boolean;
}> = ({
  data = {
    value: 1250,
    label: 'کل بازدیدکنندگان',
    unit: 'نفر',
    trend: 'up',
    trendValue: 12.5,
  },
  title,
  className = '',
  size = 'medium',
  showTrend = true,
}) => {
  const sizeClasses = {
    small: 'text-2xl',
    medium: 'text-4xl',
    large: 'text-6xl',
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'up':
        return '↗';
      case 'down':
        return '↘';
      default:
        return '→';
    }
  };

  const getTrendColor = (trend?: string) => {
    switch (trend) {
      case 'up':
        return '#5BF7FA';
      case 'down':
        return '#ff6b6b';
      default:
        return '#9EFEFF';
    }
  };

  return (
    <div className={`h-full w-full rounded-[5px] border border-[#323538] bg-[#181A1B] p-6 ${className}`}>
      {title && (
        <h3 className="mb-4 text-lg font-semibold text-white" style={{ fontFamily: 'IranYekanX' }}>
          {title}
        </h3>
      )}
      <div className="flex h-full flex-col items-center justify-center text-center">
        <div
          className={`${sizeClasses[size]} font-bold mb-2`}
          style={{ 
            color: CHART_COLORS[3],
            fontFamily: 'IranYekanX',
          }}
        >
          {data.value}
          {data.unit && (
            <span className="text-lg text-gray-400 mr-2">
              {data.unit}
            </span>
          )}
        </div>
        
        {data.label && (
          <div className="text-lg text-gray-300 mb-3" style={{ fontFamily: 'IranYekanX' }}>
            {data.label}
          </div>
        )}

        {showTrend && data.trend && data.trendValue && (
          <div 
            className="flex items-center text-sm font-medium"
            style={{ 
              color: getTrendColor(data.trend),
              fontFamily: 'IranYekanX',
            }}
          >
            <span className="text-lg ml-1">
              {getTrendIcon(data.trend)}
            </span>
            {data.trendValue}%
            <span className="text-gray-400 mr-2">
              نسبت به قبل
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * نمایش لیستی مطالب
 */
export const ContentList: React.FC<{
  data?: ListItem[];
  title?: string;
  className?: string;
  maxHeight?: string;
  showCategory?: boolean;
  showDate?: boolean;
  showValue?: boolean;
  itemsPerPage?: number;
}> = ({
  data = [
    {
      id: 1,
      title: 'تحلیل ترندهای شبکه‌های اجتماعی در سال ۱۴۰۳',
      description: 'بررسی جامع تغییرات و روندهای جدید در پلتفرم‌های مختلف',
      value: 1250,
      date: '۱۴۰۳/۰۸/۱۵',
      category: 'تحلیل',
      url: '#',
    },
    {
      id: 2,
      title: 'گزارش عملکرد کمپین‌های تبلیغاتی',
      description: 'نتایج و آمار کمپین‌های تبلیغاتی ماه گذشته',
      value: 890,
      date: '۱۴۰۳/۰۸/۱۰',
      category: 'گزارش',
      url: '#',
    },
    {
      id: 3,
      title: 'تحلیل احساسات کاربران در پلتفرم تلگرام',
      description: 'بررسی نظرات و احساسات کاربران نسبت به محتوای ارائه شده',
      value: 456,
      date: '۱۴۰۳/۰۸/۰۵',
      category: 'تحلیل',
      url: '#',
    },
  ],
  title = 'لیست مطالب',
  className = '',
  maxHeight = '500px',
  showCategory = true,
  showDate = true,
  showValue = true,
}) => {
  return (
    <div className={`h-full w-full rounded-[5px] border border-[#323538] bg-[#181A1B] p-4 ${className}`}>
      {title && (
        <h3 className="mb-4 text-lg font-semibold text-white" style={{ fontFamily: 'IranYekanX' }}>
          {title}
        </h3>
      )}
      <div 
        className="overflow-auto"
        style={{ maxHeight }}
      >
        <div className="space-y-3">
          {data.map((item, index) => (
            <div
              key={item.id}
              className="rounded-lg border border-[#374151] bg-[#1F2937] p-4 transition-colors hover:bg-[#374151]"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 
                    className="text-base font-semibold text-white mb-2"
                    style={{ fontFamily: 'IranYekanX' }}
                  >
                    {item.url ? (
                      <a href={item.url} className="hover:text-[#5BF7FA] transition-colors">
                        {item.title}
                      </a>
                    ) : (
                      item.title
                    )}
                  </h4>
                  
                  {item.description && (
                    <p 
                      className="text-sm text-gray-400 mb-3"
                      style={{ fontFamily: 'IranYekanX' }}
                    >
                      {item.description}
                    </p>
                  )}
                  
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    {showCategory && item.category && (
                      <span 
                        className="px-2 py-1 rounded"
                        style={{ 
                          backgroundColor: CHART_COLORS[1],
                          color: 'white',
                          fontFamily: 'IranYekanX',
                        }}
                      >
                        {item.category}
                      </span>
                    )}
                    
                    {showDate && item.date && (
                      <span style={{ fontFamily: 'IranYekanX' }}>
                        📅 {item.date}
                      </span>
                    )}
                  </div>
                </div>
                
                {showValue && item.value && (
                  <div className="text-left">
                    <div 
                      className="text-lg font-bold"
                      style={{ 
                        color: CHART_COLORS[3],
                        fontFamily: 'IranYekanX',
                      }}
                    >
                      {item.value}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
