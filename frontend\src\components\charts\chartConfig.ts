import Highcharts from 'highcharts';

/**
 * طیف رنگی مشترک برای همه چارت‌ها
 */
export const CHART_COLORS = [
  '#004748',
  '#026B6E',
  '#048F92',
  '#5BF7FA',
  '#9EFEFF',
  '#D0FEFF',
  '#FFFFFF',
];

/**
 * تنظیمات مشترک برای legend
 */
export const LEGEND_CONFIG: Highcharts.LegendOptions = {
  enabled: true,
  align: 'right',
  verticalAlign: 'middle',
  layout: 'vertical',
  itemStyle: {
    color: '#ffffff',
    fontSize: '12px',
    fontFamily: 'IranYekanX',
    fontWeight: 'normal',
  },
  itemHoverStyle: {
    color: '#5BF7FA',
  },
  itemHiddenStyle: {
    color: '#666666',
  },
  symbolRadius: 6,
  symbolHeight: 12,
  symbolWidth: 12,
  itemMarginTop: 5,
  itemMarginBottom: 5,
  margin: 20,
};

/**
 * تنظیمات مشترک برای title
 */
export const TITLE_CONFIG: Highcharts.TitleOptions = {
  style: {
    color: '#ffffff',
    fontFamily: 'IranYekanX',
    fontSize: '16px',
    fontWeight: 'bold',
  },
  align: 'center',
};

/**
 * تنظیمات مشترک برای tooltip
 */
export const TOOLTIP_CONFIG: Highcharts.TooltipOptions = {
  backgroundColor: 'rgba(0, 0, 0, 0.8)',
  borderColor: '#026B6E',
  borderRadius: 8,
  borderWidth: 1,
  style: {
    color: '#ffffff',
    fontFamily: 'IranYekanX',
    fontSize: '12px',
  },
  shadow: true,
};

/**
 * تنظیمات مشترک برای محورها
 */
export const AXIS_CONFIG = {
  xAxis: {
    gridLineColor: '#374151',
    lineColor: '#374151',
    tickColor: '#374151',
    labels: {
      style: {
        color: '#ffffff',
        fontFamily: 'IranYekanX',
        fontSize: '11px',
      },
    },
    title: {
      style: {
        color: '#ffffff',
        fontFamily: 'IranYekanX',
        fontSize: '12px',
      },
    },
  } as Highcharts.XAxisOptions,

  yAxis: {
    gridLineColor: '#374151',
    lineColor: '#374151',
    tickColor: '#374151',
    labels: {
      style: {
        color: '#ffffff',
        fontFamily: 'IranYekanX',
        fontSize: '11px',
      },
    },
    title: {
      style: {
        color: '#ffffff',
        fontFamily: 'IranYekanX',
        fontSize: '12px',
      },
    },
  } as Highcharts.YAxisOptions,
};

/**
 * تنظیمات پایه مشترک برای همه چارت‌ها
 */
export const BASE_CHART_CONFIG: Partial<Highcharts.Options> = {
  colors: CHART_COLORS,
  chart: {
    backgroundColor: 'transparent',
    animation: false,
    style: {
      fontFamily: 'IranYekanX',
    },
  },
  title: TITLE_CONFIG,
  legend: LEGEND_CONFIG,
  tooltip: TOOLTIP_CONFIG,
  credits: {
    enabled: false,
  },
  plotOptions: {
    series: {
      animation: false,
      dataLabels: {
        style: {
          color: '#ffffff',
          fontFamily: 'IranYekanX',
          fontSize: '10px',
          textOutline: 'none',
        },
      },
    },
  },
};

/**
 * تابع کمکی برای merge کردن nested objects
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mergeObjects = (target: any, source: any): any => {
  if (!source || typeof source !== 'object') return target;
  if (!target || typeof target !== 'object') return source;

  const result = { ...target };

  Object.keys(source).forEach((key) => {
    if (
      source[key] &&
      typeof source[key] === 'object' &&
      !Array.isArray(source[key])
    ) {
      result[key] = mergeObjects(target[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  });

  return result;
};

/**
 * تابع کمکی برای ایجاد تنظیمات چارت با legend اختیاری
 */
export const createChartConfig = (
  baseConfig: Partial<Highcharts.Options>,
  showLegend: boolean = true
): Partial<Highcharts.Options> => {
  // ابتدا BASE_CHART_CONFIG و baseConfig را merge می‌کنیم
  const mergedConfig = mergeObjects(BASE_CHART_CONFIG, baseConfig);

  // سپس legend را بر اساس showLegend تنظیم می‌کنیم
  let finalLegendConfig: Highcharts.LegendOptions;

  if (showLegend) {
    // اگر baseConfig.legend وجود دارد، آن را با LEGEND_CONFIG merge می‌کنیم
    finalLegendConfig = baseConfig.legend
      ? mergeObjects(LEGEND_CONFIG, baseConfig.legend)
      : LEGEND_CONFIG;
  } else {
    finalLegendConfig = { enabled: false };
  }

  return {
    ...mergedConfig,
    legend: finalLegendConfig,
  };
};

/**
 * تابع کمکی برای تنظیم responsive rules
 */
export const RESPONSIVE_RULES: Highcharts.ResponsiveOptions = {
  rules: [
    {
      condition: {
        maxWidth: 500,
      },
      chartOptions: {
        legend: {
          align: 'center',
          verticalAlign: 'bottom',
          layout: 'horizontal',
          itemStyle: {
            fontSize: '10px',
          },
        },
        title: {
          style: {
            fontSize: '14px',
          },
        },
      },
    },
    {
      condition: {
        maxWidth: 300,
      },
      chartOptions: {
        legend: {
          enabled: false,
        },
        title: {
          style: {
            fontSize: '12px',
          },
        },
      },
    },
  ],
};
